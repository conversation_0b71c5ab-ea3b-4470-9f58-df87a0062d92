#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单提交器 - 简单的GUI程序用于批量提交订单
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import json
import threading
import time
from datetime import datetime
import os


class OrderSubmitter:
    def __init__(self, root):
        self.root = root
        self.root.title("订单提交器")
        self.root.geometry("600x500")
        
        # 配置文件路径
        self.config_file = "电话配置.txt"
        
        # 代理设置
        self.proxy_url = "http://127.0.0.1:7897"
        self.proxies = {
            'http': self.proxy_url,
            'https': self.proxy_url
        }
        
        # 目标URL和固定参数
        self.target_url = "https://yh.lt-666.com/index/ajax/order/act/add.html"
        self.fixed_time = "12"
        
        self.setup_ui()
        self.load_phones()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="订单提交器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 电话号码输入区域
        phone_label = ttk.Label(main_frame, text="电话号码 (每行一个):")
        phone_label.grid(row=1, column=0, sticky=tk.NW, padx=(0, 10))
        
        self.phone_text = scrolledtext.ScrolledText(main_frame, width=40, height=15)
        self.phone_text.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 绑定文本变化事件，实时保存
        self.phone_text.bind('<KeyRelease>', self.on_phone_change)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        # 验证代理按钮
        self.verify_proxy_btn = ttk.Button(button_frame, text="验证代理", command=self.verify_proxy)
        self.verify_proxy_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 提交订单按钮
        self.submit_btn = ttk.Button(button_frame, text="提交订单", command=self.submit_orders)
        self.submit_btn.pack(side=tk.LEFT)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="就绪", foreground="green")
        self.status_label.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
    def load_phones(self):
        """从配置文件加载电话号码"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    self.phone_text.delete(1.0, tk.END)
                    self.phone_text.insert(1.0, content)
                print(f"已加载配置文件: {self.config_file}")
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            
    def save_phones(self):
        """保存电话号码到配置文件"""
        try:
            content = self.phone_text.get(1.0, tk.END).strip()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            
    def on_phone_change(self, event):
        """电话号码文本变化时的回调"""
        # 延迟保存，避免频繁写入
        if hasattr(self, '_save_timer'):
            self.root.after_cancel(self._save_timer)
        self._save_timer = self.root.after(1000, self.save_phones)
        
    def verify_proxy(self):
        """验证代理是否正常工作"""
        def verify_thread():
            try:
                self.update_status("正在验证代理...", "orange")
                
                # 测试代理连接
                test_url = "http://httpbin.org/ip"
                response = requests.get(test_url, proxies=self.proxies, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    proxy_ip = result.get('origin', '未知')
                    self.update_status(f"代理验证成功 - IP: {proxy_ip}", "green")
                    print(f"代理验证成功 - 当前IP: {proxy_ip}")
                    messagebox.showinfo("代理验证", f"代理连接正常\n当前IP: {proxy_ip}")
                else:
                    self.update_status("代理验证失败", "red")
                    print(f"代理验证失败 - 状态码: {response.status_code}")
                    messagebox.showerror("代理验证", "代理连接失败")
                    
            except Exception as e:
                self.update_status("代理验证失败", "red")
                print(f"代理验证异常: {e}")
                messagebox.showerror("代理验证", f"代理连接异常: {e}")
                
        # 在新线程中执行验证
        threading.Thread(target=verify_thread, daemon=True).start()
        
    def submit_orders(self):
        """提交订单"""
        phones = self.get_phone_list()
        if not phones:
            messagebox.showwarning("警告", "请输入至少一个电话号码")
            return
            
        def submit_thread():
            try:
                self.update_status("正在提交订单...", "orange")
                success_count = 0
                total_count = len(phones)
                
                for i, phone in enumerate(phones, 1):
                    try:
                        # 准备POST数据
                        data = {
                            'phone': phone,
                            'time': self.fixed_time
                        }
                        
                        # 发送POST请求
                        response = requests.post(
                            self.target_url,
                            data=data,
                            proxies=self.proxies,
                            timeout=30
                        )
                        
                        # 打印响应内容
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        print(f"[{timestamp}] 电话: {phone} - 响应: {response.text}")
                        
                        # 检查响应
                        if response.status_code == 200:
                            try:
                                result = response.json()
                                if result.get('code') == 'ok':
                                    success_count += 1
                            except json.JSONDecodeError:
                                pass
                                
                        # 更新进度
                        self.update_status(f"进度: {i}/{total_count} (成功: {success_count})", "blue")
                        
                        # 短暂延迟避免请求过快
                        time.sleep(0.5)
                        
                    except Exception as e:
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        print(f"[{timestamp}] 电话: {phone} - 错误: {e}")
                        
                # 完成提示
                self.update_status(f"提交完成 - 成功: {success_count}/{total_count}", "green")
                messagebox.showinfo("提交完成", f"订单提交完成\n成功: {success_count}/{total_count}")
                
            except Exception as e:
                self.update_status("提交失败", "red")
                print(f"提交订单异常: {e}")
                messagebox.showerror("错误", f"提交订单失败: {e}")
                
        # 在新线程中执行提交
        threading.Thread(target=submit_thread, daemon=True).start()
        
    def get_phone_list(self):
        """获取电话号码列表"""
        content = self.phone_text.get(1.0, tk.END).strip()
        phones = [line.strip() for line in content.split('\n') if line.strip()]
        return phones
        
    def update_status(self, message, color="black"):
        """更新状态标签"""
        self.root.after(0, lambda: self.status_label.config(text=message, foreground=color))


def main():
    """主函数"""
    print("订单提交器启动中...")
    print("代理设置: http://127.0.0.1:7897")
    print("目标URL: https://yh.lt-666.com/index/ajax/order/act/add.html")
    print("-" * 50)
    
    root = tk.Tk()
    app = OrderSubmitter(root)
    root.mainloop()


if __name__ == "__main__":
    main()
