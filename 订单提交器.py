#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单提交器 - 简单的GUI程序用于批量提交订单
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import json
import threading
import time
from datetime import datetime
import os


class OrderSubmitter:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 雷霆订单提交器")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)

        # 设置全局字体
        self.font_family = "Microsoft YaHei"
        self.setup_fonts()

        # 配置文件路径
        self.config_file = "电话配置.txt"
        self.cookie_file = "Cookie配置.txt"

        # 代理设置
        self.proxy_url = "http://127.0.0.1:7897"
        self.proxies = {
            'http': self.proxy_url,
            'https': self.proxy_url
        }

        # 目标URL和固定参数
        self.target_url = "https://yh.lt-666.com/index/ajax/order/act/add.html"
        self.fixed_time = "12"

        self.setup_ui()
        self.load_phones()
        self.load_cookies()

    def setup_fonts(self):
        """设置全局字体"""
        self.title_font = (self.font_family, 18, "bold")
        self.label_font = (self.font_family, 10)
        self.button_font = (self.font_family, 10)
        self.text_font = (self.font_family, 9)

        # 配置ttk样式
        style = ttk.Style()
        style.configure('Title.TLabel', font=self.title_font)
        style.configure('Heading.TLabel', font=(self.font_family, 11, "bold"))
        style.configure('Custom.TButton', font=self.button_font)

    def setup_ui(self):
        """设置用户界面"""
        # 设置根窗口背景色
        self.root.configure(bg='#f0f0f0')

        # 创建主容器
        main_container = tk.Frame(self.root, bg='#f0f0f0')
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 配置网格权重
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(1, weight=1)

        # 标题区域
        self.setup_title_area(main_container)

        # 主工作区域
        self.setup_main_area(main_container)

        # 底部状态区域
        self.setup_status_area(main_container)

    def setup_title_area(self, parent):
        """设置标题区域"""
        title_frame = tk.Frame(parent, bg='#2c3e50', height=60)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)

        title_label = tk.Label(
            title_frame,
            text="🚀 雷霆订单提交器",
            font=self.title_font,
            bg='#2c3e50',
            fg='white'
        )
        title_label.pack(expand=True)

    def setup_main_area(self, parent):
        """设置主工作区域"""
        # 创建主工作框架
        work_frame = tk.Frame(parent, bg='#f0f0f0')
        work_frame.pack(fill=tk.BOTH, expand=True)

        # 配置网格
        work_frame.columnconfigure(0, weight=1)
        work_frame.columnconfigure(1, weight=1)
        work_frame.rowconfigure(0, weight=1)

        # 左侧面板
        self.setup_left_panel(work_frame)

        # 右侧面板
        self.setup_right_panel(work_frame)

    def setup_left_panel(self, parent):
        """设置左侧面板"""
        left_frame = tk.LabelFrame(
            parent,
            text="📱 配置区域",
            font=(self.font_family, 11, "bold"),
            bg='white',
            padx=10,
            pady=10
        )
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(1, weight=1)
        left_frame.rowconfigure(3, weight=1)

        # 电话号码区域
        phone_label = tk.Label(
            left_frame,
            text="电话号码 (每行一个):",
            font=self.label_font,
            bg='white'
        )
        phone_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        self.phone_text = scrolledtext.ScrolledText(
            left_frame,
            width=30,
            height=8,
            font=self.text_font,
            wrap=tk.WORD
        )
        self.phone_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        self.phone_text.bind('<KeyRelease>', self.on_phone_change)

        # Cookie区域
        cookie_label = tk.Label(
            left_frame,
            text="🍪 Cookie信息:",
            font=self.label_font,
            bg='white'
        )
        cookie_label.grid(row=2, column=0, sticky=tk.W, pady=(0, 5))

        self.cookie_text = scrolledtext.ScrolledText(
            left_frame,
            width=30,
            height=6,
            font=self.text_font,
            wrap=tk.WORD
        )
        self.cookie_text.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        self.cookie_text.bind('<KeyRelease>', self.on_cookie_change)

        # 按钮区域
        self.setup_button_area(left_frame)

    def setup_button_area(self, parent):
        """设置按钮区域"""
        button_frame = tk.Frame(parent, bg='white')
        button_frame.grid(row=4, column=0, pady=10)

        # 验证代理按钮
        self.verify_proxy_btn = tk.Button(
            button_frame,
            text="🔍 验证代理",
            font=self.button_font,
            bg='#3498db',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            command=self.verify_proxy
        )
        self.verify_proxy_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 提交订单按钮
        self.submit_btn = tk.Button(
            button_frame,
            text="🚀 提交订单",
            font=self.button_font,
            bg='#e74c3c',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            command=self.submit_orders
        )
        self.submit_btn.pack(side=tk.LEFT)

    def setup_right_panel(self, parent):
        """设置右侧面板"""
        right_frame = tk.LabelFrame(
            parent,
            text="📋 日志输出",
            font=(self.font_family, 11, "bold"),
            bg='white',
            padx=10,
            pady=10
        )
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(0, weight=1)

        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            right_frame,
            width=40,
            height=25,
            font=self.text_font,
            bg='#2c3e50',
            fg='#ecf0f1',
            insertbackground='white',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 清空日志按钮
        clear_log_btn = tk.Button(
            right_frame,
            text="🗑️ 清空日志",
            font=self.button_font,
            bg='#95a5a6',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            command=self.clear_log
        )
        clear_log_btn.grid(row=1, column=0, pady=(10, 0))

    def setup_status_area(self, parent):
        """设置状态区域"""
        status_frame = tk.Frame(parent, bg='#34495e', height=40)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            text="✅ 就绪",
            font=self.label_font,
            bg='#34495e',
            fg='#ecf0f1'
        )
        self.status_label.pack(side=tk.LEFT, padx=10, expand=True, anchor=tk.W)
        
    def load_phones(self):
        """从配置文件加载电话号码"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    self.phone_text.delete(1.0, tk.END)
                    self.phone_text.insert(1.0, content)
                self.log_message(f"✅ 已加载电话配置文件: {self.config_file}")
        except Exception as e:
            self.log_message(f"❌ 加载电话配置文件失败: {e}")

    def save_phones(self):
        """保存电话号码到配置文件"""
        try:
            content = self.phone_text.get(1.0, tk.END).strip()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            self.log_message(f"❌ 保存电话配置文件失败: {e}")

    def load_cookies(self):
        """从配置文件加载Cookie"""
        try:
            if os.path.exists(self.cookie_file):
                with open(self.cookie_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    self.cookie_text.delete(1.0, tk.END)
                    self.cookie_text.insert(1.0, content)
                self.log_message(f"✅ 已加载Cookie配置文件: {self.cookie_file}")
        except Exception as e:
            self.log_message(f"❌ 加载Cookie配置文件失败: {e}")

    def save_cookies(self):
        """保存Cookie到配置文件"""
        try:
            content = self.cookie_text.get(1.0, tk.END).strip()
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            self.log_message(f"❌ 保存Cookie配置文件失败: {e}")

    def on_phone_change(self, event):
        """电话号码文本变化时的回调"""
        # 延迟保存，避免频繁写入
        if hasattr(self, '_save_timer'):
            self.root.after_cancel(self._save_timer)
        self._save_timer = self.root.after(1000, self.save_phones)

    def on_cookie_change(self, event):
        """Cookie文本变化时的回调"""
        # 延迟保存，避免频繁写入
        if hasattr(self, '_cookie_timer'):
            self.root.after_cancel(self._cookie_timer)
        self._cookie_timer = self.root.after(1000, self.save_cookies)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 在主线程中更新UI
        def update_log():
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)

        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)

        # 同时输出到控制台
        print(log_entry.strip())

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log_message("🗑️ 日志已清空")
        
    def verify_proxy(self):
        """验证代理是否正常工作"""
        def verify_thread():
            try:
                self.update_status("🔍 正在验证代理...", "#f39c12")
                self.log_message("🔍 开始验证代理连接...")

                # 测试代理连接
                test_url = "http://httpbin.org/ip"
                response = requests.get(test_url, proxies=self.proxies, timeout=10)

                if response.status_code == 200:
                    result = response.json()
                    proxy_ip = result.get('origin', '未知')
                    self.update_status(f"✅ 代理验证成功 - IP: {proxy_ip}", "#27ae60")
                    self.log_message(f"✅ 代理验证成功 - 当前IP: {proxy_ip}")
                    messagebox.showinfo("代理验证", f"代理连接正常\n当前IP: {proxy_ip}")
                else:
                    self.update_status("❌ 代理验证失败", "#e74c3c")
                    self.log_message(f"❌ 代理验证失败 - 状态码: {response.status_code}")
                    messagebox.showerror("代理验证", "代理连接失败")

            except Exception as e:
                self.update_status("❌ 代理验证失败", "#e74c3c")
                self.log_message(f"❌ 代理验证异常: {e}")
                messagebox.showerror("代理验证", f"代理连接异常: {e}")

        # 在新线程中执行验证
        threading.Thread(target=verify_thread, daemon=True).start()
        
    def submit_orders(self):
        """提交订单"""
        phones = self.get_phone_list()
        if not phones:
            messagebox.showwarning("警告", "请输入至少一个电话号码")
            return

        def submit_thread():
            try:
                self.update_status("🚀 正在提交订单...", "#f39c12")
                self.log_message(f"🚀 开始批量提交订单，共 {len(phones)} 个电话号码")

                success_count = 0
                total_count = len(phones)

                # 获取Cookie
                cookies = self.get_cookies()

                for i, phone in enumerate(phones, 1):
                    try:
                        # 准备POST数据
                        data = {
                            'phone': phone,
                            'time': self.fixed_time
                        }

                        # 准备请求头
                        headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'Accept': 'application/json, text/javascript, */*; q=0.01',
                            'X-Requested-With': 'XMLHttpRequest'
                        }

                        # 发送POST请求
                        response = requests.post(
                            self.target_url,
                            data=data,
                            headers=headers,
                            cookies=cookies,
                            proxies=self.proxies,
                            timeout=30
                        )

                        # 记录响应
                        response_text = response.text.strip()

                        # 检查响应
                        success = False
                        if response.status_code == 200:
                            try:
                                result = response.json()
                                if result.get('code') == 'ok':
                                    success = True
                                    success_count += 1
                                    self.log_message(f"✅ 电话: {phone} - 提交成功: {response_text}")
                                else:
                                    self.log_message(f"❌ 电话: {phone} - 提交失败: {response_text}")
                            except json.JSONDecodeError:
                                self.log_message(f"⚠️ 电话: {phone} - 响应格式异常: {response_text}")
                        else:
                            self.log_message(f"❌ 电话: {phone} - HTTP错误 {response.status_code}: {response_text}")

                        # 更新进度
                        progress_text = f"📊 进度: {i}/{total_count} (成功: {success_count})"
                        self.update_status(progress_text, "#3498db")

                        # 短暂延迟避免请求过快
                        time.sleep(0.8)

                    except Exception as e:
                        self.log_message(f"❌ 电话: {phone} - 请求异常: {e}")

                # 完成提示
                final_status = f"✅ 提交完成 - 成功: {success_count}/{total_count}"
                self.update_status(final_status, "#27ae60")
                self.log_message(f"🎉 批量提交完成！成功: {success_count}/{total_count}")
                messagebox.showinfo("提交完成", f"订单提交完成\n成功: {success_count}/{total_count}")

            except Exception as e:
                self.update_status("❌ 提交失败", "#e74c3c")
                self.log_message(f"❌ 提交订单异常: {e}")
                messagebox.showerror("错误", f"提交订单失败: {e}")

        # 在新线程中执行提交
        threading.Thread(target=submit_thread, daemon=True).start()
        
    def get_phone_list(self):
        """获取电话号码列表"""
        content = self.phone_text.get(1.0, tk.END).strip()
        phones = [line.strip() for line in content.split('\n') if line.strip()]
        return phones

    def get_cookies(self):
        """获取Cookie字典"""
        cookie_content = self.cookie_text.get(1.0, tk.END).strip()
        cookies = {}

        if cookie_content:
            try:
                # 解析Cookie字符串
                for item in cookie_content.split(';'):
                    item = item.strip()
                    if '=' in item:
                        key, value = item.split('=', 1)
                        cookies[key.strip()] = value.strip()
                self.log_message(f"🍪 已解析 {len(cookies)} 个Cookie")
            except Exception as e:
                self.log_message(f"❌ Cookie解析失败: {e}")

        return cookies

    def update_status(self, message, color="#ecf0f1"):
        """更新状态标签"""
        def update():
            self.status_label.config(text=message, fg=color)
        self.root.after(0, update)


def main():
    """主函数"""
    print("🚀 雷霆订单提交器启动中...")
    print("📡 代理设置: http://127.0.0.1:7897")
    print("🌐 目标URL: https://yh.lt-666.com/index/ajax/order/act/add.html")
    print("=" * 60)

    root = tk.Tk()
    OrderSubmitter(root)
    root.mainloop()


if __name__ == "__main__":
    main()
